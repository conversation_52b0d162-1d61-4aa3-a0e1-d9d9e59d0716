import type { Config } from 'tailwindcss';

const config: Config = {
    content: [
        './pages/**/*.{js,ts,jsx,tsx,mdx}',
        './components/**/*.{js,ts,jsx,tsx,mdx}',
        './app/**/*.{js,ts,jsx,tsx,mdx}'
    ],
    theme: {
        extend: {
            colors: {
                'qb-gray-5': '#FAFAFB',
                'qb-gray-20': '#FBF9FA',
                'qb-gray-30': '#FBF9FF',
                'qb-gray-40': '#F8F9FA',
                'qb-gray-50': '#FBFBFB',
                'qb-gray-100': '#F2F2F2',
                'qb-gray-103': '#EEEEEE',
                'qb-gray-105': '#E6E7EA',
                'qb-gray-110': '#EEEFF1',
                'qb-gray-115': '#E5E7EB',
                'qb-gray-125': '#F6F3FE',
                'qb-gray-150': '#6B7280',
                'qb-gray-200': '#7D7F84',

                'qb-black-100': '#232529',

                'qb-red-50': '#FEECF1',
                'qb-red-70': '#FFDEE8',
                'qb-red-100': '#991B1B',

                'qb-green-30': '#F4FBCB',
                'qb-green-50': '#ADEEA83D',
                'qb-green-70': '#6a7a20',

                'purple-60': '#F1F0FD',
                'qb-purple-100': '#D0BCFF',
            },
            backgroundImage: {
                'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
                'gradient-conic':
                    'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))'
            },
            animation: {
                'gradient-x': 'gradient-x 5s ease infinite',
                'gradient-y': 'gradient-y 5s ease infinite',
                'gradient-xy': 'gradient-xy 5s ease infinite'
            },
            keyframes: {
                'gradient-y': {
                    '0%, 100%': {
                        'background-size': '400% 400%',
                        'background-position': 'center top'
                    },
                    '50%': {
                        'background-size': '200% 200%',
                        'background-position': 'center center'
                    }
                },
                'gradient-x': {
                    '0%, 100%': {
                        'background-size': '200% 200%',
                        'background-position': 'left center'
                    },
                    '50%': {
                        'background-size': '200% 200%',
                        'background-position': 'right center'
                    }
                },
                'gradient-xy': {
                    '0%, 100%': {
                        'background-size': '400% 400%',
                        'background-position': 'left center'
                    },
                    '50%': {
                        'background-size': '200% 200%',
                        'background-position': 'right center'
                    }
                }
            }
        }
    },
    plugins: [require('@tailwindcss/forms')]
};
export default config;
