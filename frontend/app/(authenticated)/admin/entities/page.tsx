'use client';

import React, { useState } from 'react';

import {
    Dialog,
    DialogPanel,
    DialogTitle,
    Transition,
    TransitionChild
} from '@headlessui/react';

import useEntities from '@/api/hooks/admin/useEntities';
import { ListedEntity } from '@quarterback/types';
import { AddEntityForm } from './AddEntityForm';

function AddEntityModal({
    open,
    setOpen,
    entity
}: {
    open: boolean;
    setOpen: (value: boolean) => void;
    entity?: ListedEntity;
}) {
    function handleSubmit() {
        setOpen(false);
    }

    return (
        <Transition show={open}>
            <Dialog className="relative z-50" onClose={setOpen}>
                <TransitionChild
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0">
                    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                </TransitionChild>

                <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                    <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <TransitionChild
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                            <DialogPanel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                                <DialogTitle
                                    as="h3"
                                    className="text-base font-semibold leading-6 text-gray-900">
                                    Add Exchange Listed Entity
                                </DialogTitle>
                                <AddEntityForm entity={entity} onSubmit={handleSubmit} />
                                <div className="mt-6 flex items-center justify-end gap-x-6">
                                    <button
                                        type="button"
                                        className="text-sm font-semibold leading-6 text-gray-900"
                                        onClick={() => setOpen(false)}>
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        form="add-entity"
                                        className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                        Save
                                    </button>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}

export default function ListedEntities() {
    const { data: entities } = useEntities();
    const [modalOpen, setModalOpen] = useState(false);
    const [editing, setEditing] = useState<ListedEntity | undefined>(undefined);

    return (
        <div className="p-4 sm:p-6 lg:p-8">
            <div className="flex items-center">
                <div className="flex-auto">
                    <h1 className="text-base font-semibold leading-6 text-gray-900">
                        Listed entities
                    </h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Exchange listed entities for which we collect data and insights.
                    </p>
                </div>
                <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                    <button
                        type="button"
                        onClick={() => {
                            setEditing(undefined);
                            setModalOpen(true);
                        }}
                        className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        Add entity
                    </button>
                </div>
            </div>
            <AddEntityModal open={modalOpen} setOpen={setModalOpen} entity={editing} />
            <div className="mt-8 w-full overflow-x-auto">
                <div className="inline-block min-w-full align-middle">
                    <table className="min-w-full table-fixed divide-y divide-gray-300">
                        <thead>
                            <tr>
                                <th className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0 w-40 break-words">
                                    Name
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-24 break-words">
                                    Symbol
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-24 break-words">
                                    Exchange
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    Twitter username
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    Twitter search term
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    LinkedIn search term
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    LinkedIn Username
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    News search term
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-40 break-words">
                                    Reddit search term
                                </th>
                                <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-64 break-words">
                                    About
                                </th>
                                <th className="relative py-3.5 pl-3 pr-4 sm:pr-0 w-16">
                                    <span className="sr-only">Edit</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                            {(entities ?? []).map((entity) => (
                                <tr key={`${entity.symbol}:${entity.exchange}`}>
                                    <td className="whitespace-normal break-words py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                        {entity.name}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.symbol}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.exchange}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.twitterUsername}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.twitterQuery}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.linkedInQuery}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.linkedInUsername}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.newsQuery}
                                    </td>
                                    <td className="whitespace-normal break-words px-3 py-4 text-sm text-gray-500">
                                        {entity.redditQuery}
                                    </td>
                                    <td className="px-3 py-4 text-sm text-gray-500">
                                        <div className="line-clamp-2 break-words">
                                            {entity.about}
                                        </div>
                                    </td>
                                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                        <button
                                            onClick={() => {
                                                setEditing(entity);
                                                setModalOpen(true);
                                            }}
                                            className="text-indigo-600 hover:text-indigo-900">
                                            Edit
                                            <span className="sr-only">
                                                , {entity.name}
                                            </span>
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
