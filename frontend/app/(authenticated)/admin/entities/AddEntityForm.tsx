'use client';
import useEntitiesMutation from '@/api/hooks/admin/mutations/useEntitiesMutation';
import { ListedEntity } from '@quarterback/types';
import React, { useState, useEffect, FormEvent } from 'react';

export function AddEntityForm({
    entity,
    onSubmit
}: {
    entity?: ListedEntity;
    onSubmit: () => void;
}) {
    const { trigger } = useEntitiesMutation();

    const [name, setName] = useState(entity?.name ?? '');
    const [symbol, setSymbol] = useState(entity?.symbol ?? '');
    const [exchange, setExchange] = useState(entity?.exchange ?? 'ASX');
    const [twitterUsername, setTwitterUsername] = useState(entity?.twitterUsername ?? '');
    const [twitterQuery, setTwitterQuery] = useState(entity?.twitterQuery ?? '');
    const [newsQuery, setNewsQuery] = useState(entity?.newsQuery ?? '');
    const [redditQuery, setRedditQuery] = useState(entity?.redditQuery ?? '');
    const [linkedInQuery, setlinkedInQuery] = useState(entity?.linkedInQuery ?? '');
    const [linkedInUsername, setLinkedInUsername] = useState(
        entity?.linkedInUsername ?? ''
    );
    const [about, setAbout] = useState(entity?.about ?? '');

    useEffect(() => {
        if (entity) {
            setName(entity.name ?? '');
            setSymbol(entity.symbol ?? '');
            setExchange(entity.exchange ?? 'ASX');
            setTwitterUsername(entity.twitterUsername ?? '');
            setTwitterQuery(entity.twitterQuery ?? '');
            setNewsQuery(entity.newsQuery ?? '');
            setRedditQuery(entity.redditQuery ?? '');
            setlinkedInQuery(entity.linkedInQuery ?? '');
            setLinkedInUsername(entity.linkedInUsername ?? '');
            setAbout(entity.about ?? '');
        }
    }, [entity]);

    async function handleSubmit(event: FormEvent) {
        event.preventDefault();

        await trigger([
            {
                name,
                symbol,
                exchange,
                twitterUsername,
                twitterQuery,
                newsQuery,
                redditQuery,
                linkedInQuery,
                linkedInUsername,
                about
            }
        ]);

        onSubmit();
    }

    return (
        <form id="add-entity" onSubmit={handleSubmit}>
            <div className="space-y-12">
                <div className="pb-12">
                    <div className="mt-4 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                        <div className="sm:col-span-full">
                            <label
                                htmlFor="name"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Name*
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="name"
                                    id="name"
                                    placeholder="e.g. Veem Pty Ltd"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={name}
                                    onChange={(e) => setName(e.target.value)}
                                    required
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="symbol"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Ticker symbol*
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="symbol"
                                    id="symbol"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={symbol}
                                    onChange={(e) => setSymbol(e.target.value)}
                                    required
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="exchange"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Exchange*
                            </label>
                            <div className="mt-2">
                                <select
                                    id="exchange"
                                    name="exchange"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:text-sm sm:leading-6"
                                    value={exchange}
                                    disabled>
                                    <option value="ASX">ASX</option>
                                </select>
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="twitter-username"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Twitter username
                            </label>
                            <div className="mt-2 flex rounded-md shadow-sm">
                                <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 px-3 text-gray-500 sm:text-sm">
                                    @
                                </span>
                                <input
                                    type="text"
                                    name="twitter-username"
                                    id="twitter-username"
                                    className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    placeholder="e.g. veem"
                                    value={twitterUsername}
                                    onChange={(e) => setTwitterUsername(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="twitter-search-term"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Twitter search term
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="twitter-search-term"
                                    id="twitter-search-term"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={twitterQuery}
                                    onChange={(e) => setTwitterQuery(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="linkedIn-search-term"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                LinkedIn search term
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="linkedIn-search-term"
                                    id="linkedIn-search-term"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={linkedInQuery}
                                    onChange={(e) => setlinkedInQuery(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="linkedIn-username"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                LinkedIn username
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="linkedIn-username"
                                    id="linkedIn-username"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={linkedInUsername}
                                    onChange={(e) => setLinkedInUsername(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="news-search-term"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                News search term
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="news-search-term"
                                    id="news-search-term"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={newsQuery}
                                    onChange={(e) => setNewsQuery(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-3">
                            <label
                                htmlFor="news-search-term"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Reddit search term
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="news-search-term"
                                    id="news-search-term"
                                    placeholder="e.g. VEE"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={redditQuery}
                                    onChange={(e) => setRedditQuery(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="sm:col-span-full">
                            <label
                                htmlFor="about"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                About
                            </label>
                            <div className="mt-2">
                                <textarea
                                    name="about"
                                    id="about"
                                    placeholder="About the company"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={about}
                                    onChange={(e) => setAbout(e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    );
}
