'use client';

import { useOrganisation } from '@/components/OrganisationProvider';
import Button from '@/components/ui/Button';
import { AddEntityForm } from '../admin/entities/AddEntityForm';

export default function CompanySettings() {
    const { selected: { entity } = {} } = useOrganisation();

    if (!entity) {
        return null;
    }

    return (
        <div className="px-32">
            <AddEntityForm
                entity={entity}
                onSubmit={() => {
                    console.log('submit');
                }}
            />

            <Button variant="primary" type="submit" form="add-entity" size="sm">
                Save
            </Button>
        </div>
    );
}
