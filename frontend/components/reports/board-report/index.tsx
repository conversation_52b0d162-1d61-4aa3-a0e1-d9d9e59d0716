import { Document, Font, Page, StyleSheet, Text, View } from '@react-pdf/renderer';

import React, { useMemo } from 'react';
import ActivityChart from '@/components/charts/activity';
import AbnormalReturnsChart from '@/components/charts/abnormal-return';
import useChatter from '@/api/hooks/useChatter';
import {
    sentimentScore,
    useAverageSentiment,
    useSentimentBands,
    useSentimentCount
} from '@/util/sentiment';
import hasField from '@/util/hasField';
import { useReactPdfSvg } from '@/components/pdf/useReactPdfSvg';
import ReportActivityRow from '@/components/reports/board-report/ReportActivityRow';
import SummaryStatistic from '@/components/reports/board-report/SummaryStatistic';
import { Activity } from '@quarterback/types';
import Chatter from './sections/Chatter';
import Section from '@/components/reports/board-report/Section';
import Heading from '@/components/reports/board-report/sections/Heading';
import Summary from '@/components/reports/board-report/sections/Summary';
import LikesBadge from '@/components/reports/board-report/badges/LikesBadge';
import SentimentBadge from '@/components/reports/board-report/badges/SentimentBadge';
import Legend from '@/components/reports/board-report/Legend';
import useBoardReportData from '@/components/reports/board-report/useBoardReportData';
import SentimentGauge from '@/components/charts/sentiment-gauge';
import useCombinedQuotes from '@/components/hooks/useCombinedQuotes';
import { groupBy } from '@quarterback/util';
import ReportAnnouncementRow from '@/components/reports/board-report/ReportAnnouncementRow';
import { add, format } from 'date-fns';
import HorizontalBarChart from '@/components/reports/board-report/HorizontalBarChart';
import source from '@/util/source';

function likes(activity: Activity) {
    if (activity.type === 'tweet' || activity.type === 'hotcopper') {
        return activity.likes;
    } else {
        return undefined;
    }
}

Font.register({
    family: 'Inter',
    fonts: [
        {
            src: '/reports/Inter-Bold.ttf',
            fontWeight: 'bold'
        },
        {
            src: '/reports/Inter-SemiBold.ttf',
            fontWeight: 'semibold'
        },
        {
            src: '/reports/Inter-Regular.ttf',
            fontWeight: 'normal'
        }
    ]
});

export const styleSheet = StyleSheet.create({
    heading: {
        fontFamily: 'Inter',
        fontWeight: 'bold',
        fontSize: 14
    },
    subheading: {
        fontFamily: 'Inter',
        fontWeight: 'semibold',
        fontSize: 12
    },
    description: {
        fontFamily: 'Inter',
        fontWeight: 'normal',
        fontSize: 11,
        color: 'gray'
    },
    body: {
        fontFamily: 'Inter',
        fontWeight: 'normal',
        fontSize: 11
    }
});

function BulletPoint({ text }: { text: string }) {
    return (
        <View
            style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                columnGap: 4
            }}>
            <View
                style={{
                    width: 4,
                    height: 4,
                    borderRadius: 1000,
                    backgroundColor: 'gray'
                }}
            />
            <Text style={[styleSheet.body, { color: 'gray' }]}>{text}</Text>
        </View>
    );
}

function SourceSentimentBar({
    source,
    chatter
}: {
    source: string;
    chatter: Array<Activity>;
}) {
    const bands = useSentimentBands(chatter);
    const avgSentiment = useAverageSentiment(chatter);

    return (
        <View
            style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 2
            }}>
            <View style={{ width: 84, marginRight: 2 }}>
                <Text
                    wrap={false}
                    style={[
                        styleSheet.body,
                        { fontSize: 10, maxLines: 1, textOverflow: 'ellipsis' }
                    ]}>
                    {source}
                </Text>
            </View>
            <View
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    flex: 1,
                    position: 'relative',
                    marginRight: 2,
                    borderRadius: 2,
                    overflow: 'hidden'
                }}>
                <View
                    style={{
                        height: 18,
                        width: `${(bands[0] * 100).toFixed(2)}%`,
                        backgroundColor: 'rgb(239, 68, 68)'
                    }}
                />
                <View
                    style={{
                        height: 18,
                        width: `${(bands[1] * 100).toFixed(2)}%`,
                        backgroundColor: 'rgb(253, 224, 71)'
                    }}
                />
                <View
                    style={{
                        height: 18,
                        width: `${(bands[2] * 100).toFixed(2)}%`,
                        backgroundColor: 'rgb(203, 213, 225)'
                    }}
                />
                <View
                    style={{
                        height: 18,
                        width: `${(bands[3] * 100).toFixed(2)}%`,
                        backgroundColor: 'rgb(74, 222, 128)'
                    }}
                />
            </View>
            <View style={{ width: 38, flexShrink: 0, textAlign: 'center' }}>
                <Text style={[styleSheet.body, { fontSize: 10 }]}>{chatter.length}</Text>
            </View>
            <View style={{ width: 38, flexShrink: 0, textAlign: 'center' }}>
                <Text style={[styleSheet.body, { fontSize: 10 }]}>
                    {avgSentiment.toFixed(2)}
                </Text>
            </View>
        </View>
    );
}

interface Props {
    from: Date;
    to: Date;
}

export default function BoardReport({
    from,
    to,
    organisation,
    entity,
    timeSeries,
    indexSeries,
    risk,
    activities,
    followers
}: Props & Omit<ReturnType<typeof useBoardReportData>, 'loading'>) {
    const chatter = useChatter(activities);

    const priceAtClose = useMemo(
        () => timeSeries?.values?.slice(-1)?.[0]?.close,
        [timeSeries]
    );
    const priceChange = useMemo(() => {
        const open = timeSeries?.values?.[0]?.open;
        const close = timeSeries?.values?.slice(-1)?.[0]?.close;

        if (open !== undefined && close !== undefined) {
            return close / open - 1;
        } else {
            return undefined;
        }
    }, [timeSeries]);

    const combinedQuotes = useCombinedQuotes(indexSeries, timeSeries);

    const cumAbnormalReturns = useMemo(() => {
        if (risk) {
            return combinedQuotes.reduce((cumSum, quotes) => {
                const change = Math.log(quotes.stock.close / quotes.stock.open);
                const indexChange = Math.log(quotes.index.close / quotes.index.open);

                return cumSum + (change - (risk.intercept + risk.slope * indexChange));
            }, 0);
        } else {
            return 0;
        }
    }, [combinedQuotes, risk]);

    const timeSeriesByDay = useMemo(() => {
        return groupBy(timeSeries?.values ?? [], (it) => it.datetime);
    }, [timeSeries]);

    const indexSeriesByDay = useMemo(() => {
        return groupBy(indexSeries?.values ?? [], (it) => it.datetime);
    }, [indexSeries]);

    const topAuthors = useMemo((): Array<[string, number]> => {
        function author(activity: Activity) {
            switch (activity.type) {
                case 'hotcopper':
                    return activity.author?.name !== 'Hot News' &&
                        activity?.author?.name !== 'ASX News'
                        ? activity?.author?.name
                        : undefined;
                case 'tweet':
                    return `@${activity.author?.userId}`;
                case 'linkedIn':
                    return activity.author?.name;
                case 'reddit':
                    return `/u/${activity.author?.userId}`;
                default:
                    return undefined;
            }
        }

        const volume = (activities ?? []).reduce(
            (authors, activity) => {
                const activityAuthor = author(activity);

                if (activityAuthor) {
                    return {
                        ...authors,
                        [activityAuthor]: (authors[activityAuthor] ?? 0) + 1
                    };
                } else {
                    return authors;
                }
            },
            {} as Record<string, number>
        );

        return Object.entries(volume)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5);
    }, [activities]);

    const topMedia = useMemo(() => {
        function media(activity: Activity) {
            switch (activity.type) {
                case 'media':
                    return activity.source.name;
                default:
                    return undefined;
            }
        }

        const volume = (activities ?? []).reduce(
            (mediaActivities, activity) => {
                const activityMedia = media(activity);

                if (activityMedia) {
                    return {
                        ...mediaActivities,
                        [activityMedia]: (mediaActivities[activityMedia] ?? 0) + 1
                    };
                } else {
                    return mediaActivities;
                }
            },
            {} as Record<string, number>
        );

        return Object.entries(volume)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5);
    }, [activities]);

    const topEngagedChatter = useMemo(() => {
        return [...chatter]
            .filter((it) => it.type === 'hotcopper' || it.type === 'tweet')
            .sort((a, b) => {
                if (
                    (a.type === 'hotcopper' || a.type === 'tweet') &&
                    (b.type === 'hotcopper' || b.type === 'tweet')
                ) {
                    return b.likes - a.likes;
                } else {
                    return 0;
                }
            })
            .slice(0, 5);
    }, [chatter]);

    const mostNegativeChatter = useMemo(() => {
        return [...chatter]
            .filter(hasField('sentiment'))
            .filter((activity) => {
                if (activity.type === 'hotcopper') {
                    return activity.hotcopper?.sentiment !== 'BUY';
                } else {
                    return true;
                }
            })
            .sort((a, b) => {
                return sentimentScore(a.sentiment) - sentimentScore(b.sentiment);
            })
            .slice(0, 5);
    }, [chatter]);

    const avgSentiment = useAverageSentiment(chatter);

    const sentimentBands = useSentimentBands(chatter);
    const sentimentCounts = useSentimentCount(chatter);

    const chatterBySource = useMemo((): Array<[string, Array<Activity>]> => {
        const size = 5;

        const bySource = Object.entries(groupBy(chatter, (it) => source(it))).sort(
            (a, b) => b[1].length - a[1].length
        );

        return [
            ...bySource.slice(0, size),
            ['Other', bySource.slice(size).flatMap((it) => it[1])]
        ];
    }, [chatter]);

    const activityChartDom = useReactPdfSvg(
        () => (
            <ActivityChart
                width={595 - 32 - 56}
                height={300}
                style={{
                    price: {
                        yScalePadding: 0.7
                    }
                }}
                entity={entity}
                activities={activities}
                followers={{ twitter: [] }}
                timeSeries={
                    timeSeries?.values
                        ?.map((value) => ({
                            ...value
                        }))
                        .sort((a, b) => (a.datetime < b.datetime ? -1 : 1)) ?? []
                }
            />
        ),
        [timeSeries, activities]
    );

    const abnormalReturnsChartDom = useReactPdfSvg(
        () =>
            risk && combinedQuotes ? (
                <AbnormalReturnsChart
                    width={595 - 64 - 56}
                    height={128}
                    risk={risk}
                    quotes={combinedQuotes}
                />
            ) : (
                <></>
            ),
        [risk, combinedQuotes]
    );

    const sentimentGaugeDom = useReactPdfSvg(
        () => (
            <SentimentGauge
                groups={[
                    { color: 'rgb(239, 68, 68)', count: sentimentBands[0] },
                    { color: 'rgb(253, 224, 71)', count: sentimentBands[1] },
                    { color: 'rgb(203, 213, 225)', count: sentimentBands[2] },
                    { color: 'rgb(74, 222, 128)', count: sentimentBands[3] }
                ].filter((it) => it.count !== 0)}
                width={136}
            />
        ),
        [sentimentBands]
    );

    return (
        <Document>
            <Page size="A4" wrap={true} style={{ paddingVertical: 16 }}>
                <Section style={{ marginTop: 0 }}>
                    <Heading from={from} to={to} />
                </Section>

                <Section style={{ marginTop: 0 }}>
                    <Summary
                        price={
                            priceAtClose && priceChange
                                ? {
                                      current: priceAtClose,
                                      change: priceChange
                                  }
                                : undefined
                        }
                        cumulativeAbnormalReturns={cumAbnormalReturns}
                        activities={activities.length}
                        followers={followers}
                    />
                </Section>

                <Section title="Activity Volume vs Share Price">
                    <View style={{ marginLeft: -16, marginTop: 10 }}>
                        {activityChartDom}
                    </View>
                </Section>

                <Section title="Abnormal Returns vs Share Price">
                    <View style={{ paddingHorizontal: 16 }}>
                        {abnormalReturnsChartDom}
                        <Legend
                            entries={[
                                { label: 'Share price', color: '#63ABFD' },
                                { label: 'Abnormal returns', color: '#A155B9' }
                            ]}
                        />
                    </View>
                </Section>

                <Section
                    title="Chatter"
                    caption="How many mentions you received during the period and what they were saying">
                    <Chatter entity={entity} chatter={chatter} from={from} to={to} />
                </Section>
            </Page>
            <Page size="A4" wrap={true} style={{ paddingVertical: 16 }}>
                <Section
                    title="Sentiment"
                    caption="Analysis of whether chatter is positive or negative">
                    <View
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                            columnGap: 24
                        }}>
                        <SummaryStatistic
                            label="Average sentiment"
                            value={`${avgSentiment.toFixed(2)}`}
                        />

                        <View
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                columnGap: 8,
                                alignItems: 'center'
                            }}>
                            <View>{sentimentGaugeDom}</View>
                            <View>
                                <Text style={styleSheet.subheading}>
                                    Analysed {chatter.length} mentions
                                </Text>
                                <View style={{ marginLeft: 2 }}>
                                    <BulletPoint
                                        text={`${sentimentCounts[0]} had negative sentiment`}
                                    />
                                    <BulletPoint
                                        text={`${sentimentCounts[1]} had lacking sentiment`}
                                    />
                                    <BulletPoint
                                        text={`${sentimentCounts[2]} had neutral sentiment`}
                                    />
                                    <BulletPoint
                                        text={`${sentimentCounts[3]} had positive sentiment`}
                                    />
                                    <BulletPoint
                                        text={`${
                                            chatter.length -
                                            sentimentCounts[0] -
                                            sentimentCounts[1] -
                                            sentimentCounts[2] -
                                            sentimentCounts[3]
                                        } had no sentiment`}
                                    />
                                </View>
                            </View>
                        </View>
                    </View>
                </Section>

                <Section title="Sentiment by source">
                    <View style={{ paddingHorizontal: 16 }}>
                        <View
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                justifyContent: 'flex-end',
                                marginBottom: 4
                            }}>
                            <View
                                style={{
                                    flexShrink: 0,
                                    width: 38,
                                    textAlign: 'center'
                                }}>
                                <Text
                                    style={{
                                        fontFamily: 'Inter',
                                        fontWeight: 'semibold',
                                        fontSize: 8
                                    }}>
                                    Mentions
                                </Text>
                            </View>
                            <View
                                style={{
                                    flexShrink: 0,
                                    width: 38,
                                    textAlign: 'center'
                                }}>
                                <Text
                                    style={{
                                        fontFamily: 'Inter',
                                        fontWeight: 'semibold',
                                        fontSize: 8
                                    }}>
                                    Avg
                                </Text>
                            </View>
                        </View>
                        {chatterBySource.map(([source, chatter]) => (
                            <SourceSentimentBar
                                key={source}
                                source={source}
                                chatter={chatter}
                            />
                        ))}
                    </View>
                </Section>

                <Section title="Top engaged chatter">
                    <View
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            rowGap: 4
                        }}>
                        {topEngagedChatter.map((activity) => (
                            <ReportActivityRow
                                key={activity.id}
                                activity={activity}
                                badge={<LikesBadge likes={likes(activity)} />}
                            />
                        ))}
                    </View>
                </Section>

                <Section title="Most negative chatter">
                    <View
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            rowGap: 4
                        }}>
                        {mostNegativeChatter.map((activity) => (
                            <ReportActivityRow
                                key={activity.id}
                                activity={activity}
                                badge={<SentimentBadge sentiment={activity.sentiment} />}
                            />
                        ))}
                    </View>
                </Section>
                <View style={{ display: 'flex', flexDirection: 'row' }}>
                    <Section
                        title="Top authors by volume"
                        style={{ flex: 1, marginRight: -36 }}>
                        <HorizontalBarChart entries={topAuthors} />
                    </Section>
                    <Section
                        title="Top Media by volume"
                        style={{ flex: 1, marginLeft: -36 }}>
                        <HorizontalBarChart entries={topMedia} />
                    </Section>
                </View>
                <Section title="Announcements (24h impact)">
                    {activities
                        .filter((it) => it.type === 'asx-announcement')
                        .map((announcement) => {
                            const day = format(announcement.posted, 'yyyy-MM-dd');

                            return (
                                <ReportAnnouncementRow
                                    key={announcement.id}
                                    activity={announcement}
                                    risk={risk}
                                    quote={timeSeriesByDay[day]?.[0]}
                                    index={indexSeriesByDay[day]?.[0]}
                                    chatter={chatter.filter(
                                        (it) =>
                                            it.posted >= announcement.posted &&
                                            it.posted <
                                                add(announcement.posted, { days: 1 })
                                    )}
                                />
                            );
                        })}
                </Section>
                <Section title="Flagged activities">
                    {activities
                        .filter((it) => it.flagged)
                        .map((activity) => (
                            <ReportActivityRow key={activity.id} activity={activity} />
                        ))}
                </Section>
            </Page>
        </Document>
    );
}
