import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { AxisLeft } from '@visx/axis';
import { Group } from '@visx/group';
import { scaleLinear } from '@visx/scale';
import { LinePath } from '@visx/shape';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { extent } from '@visx/vendor/d3-array';
import { useMemo } from 'react';

export default function TradeVolumeChart({
    xScale,
    timeSeries,
    width,
    height,
    top
}: {
    xScale: AnyScaleBand;
    timeSeries: Array<TimeSeriesQuote>;
    width: number;
    height: number;
    top: number;
}) {
    const totalVolume = useMemo(() => {
        return [...timeSeries].filter((it) => !!xScale(it.datetime));
    }, [timeSeries, xScale]);

    const yScale = useMemo(() => {
        return scaleLinear({
            range: [height, 0],
            round: true,
            domain: extent(totalVolume, (it) => it.volume) as number[]
        });
    }, [totalVolume, height]);

    return (
        <Group left={64 + xScale.bandwidth() / 2} top={top}>
            <LinePath
                stroke="#9aa2a7"
                fill="none"
                strokeWidth={1}
                data={totalVolume}
                x={(d) => xScale(d.datetime) ?? 0}
                y={(d) => yScale(d.volume) ?? 0}
            />
        </Group>
    );
}
